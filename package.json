{"name": "pi_lawyer_ai", "version": "1.0.0", "description": "Personal Injury Lawyer AI Assistant", "scripts": {"prepare": "husky install", "test": "echo \"Error: no test specified\" && exit 1", "test:auth": "node scripts/test-auth.js", "test:jwt": "node scripts/jwt-claims-test.js", "test:routes": "node scripts/auth-route-test.js", "test:single-user": "node scripts/single-user-auth-test.js", "test:tunnel": "node scripts/test-tunnel-connection.js", "test:mcp": "jest tests/mcp.e2e.spec.ts", "mcp:rotate-key": "npx ts-node scripts/rotate-mcp-key.ts", "mcp:list-keys": "npx ts-node scripts/rotate-mcp-key.ts list", "neo4j:setup": "node scripts/setup-neo4j.js", "verify:secrets": "node scripts/verify-secrets.js", "verify:endpoint": "node scripts/verify-endpoint-config.js", "tunnel": "bash scripts/start-copilotkit-tunnel.sh", "tunnel:advanced": "bash scripts/start-copilotkit-tunnel-advanced.sh", "copilotkit:login": "npx copilotkit@latest login", "copilotkit:whoami": "npx copilotkit@latest whoami", "copilotkit:dev": "npx copilotkit@latest dev --port 8000"}, "dependencies": {"@copilotkit/cli": "^0.0.6", "@copilotkit/react-core": "^1.8.11", "@copilotkit/react-ui": "^1.8.11", "@copilotkit/runtime": "^1.7.1", "@copilotkit/shared": "^1.8.11", "@google/generative-ai": "^0.24.0", "@google-cloud/apikeys": "^3.1.0", "@google-cloud/secret-manager": "^5.6.0", "@langchain/core": "^0.3.40", "@langchain/langgraph": "^0.2.46", "@modelcontextprotocol/server-postgres": "^0.6.2", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@types/node": "^20.0.0", "commander": "^12.0.0", "google-auth-library": "^9.14.1", "langchain": "^0.1.5", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "openai": "^4.93.0", "pg": "^8.13.3", "typescript": "^5.0.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "dotenv": "^16.5.0", "eslint": "^8.57.0", "husky": "^9.1.7", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2"}, "husky": {"hooks": {"pre-commit": "npm run verify:secrets && npm run test:jwt && npm run test:auth"}}}