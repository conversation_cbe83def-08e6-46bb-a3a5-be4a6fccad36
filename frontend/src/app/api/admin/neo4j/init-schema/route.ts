/**
 * Neo4j Schema Initialization API
 * 
 * This endpoint initializes the Neo4j database schema for:
 * - Legal precedents and relationships
 * - Tenant activity tracking
 * - User behavior analysis
 * - Insights and recommendations
 * 
 * Should be run once during setup or when schema updates are needed.
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/server-exports';
import { initializeNeo4jSchema, verifyNeo4jSchema, getNeo4jStats } from '@/lib/neo4j/schema-init';

export const POST = withAuth(async (request: NextRequest, user: any) => {
  try {
    // Only allow superadmin to initialize schema
    if (!user.is_super_admin) {
      return NextResponse.json(
        { error: 'Unauthorized. Only superadmin can initialize Neo4j schema.' },
        { status: 403 }
      );
    }

    const { action } = await request.json();

    switch (action) {
      case 'initialize':
        console.log('Initializing Neo4j schema...');
        const initSuccess = await initializeNeo4jSchema();
        
        if (initSuccess) {
          const stats = await getNeo4jStats();
          return NextResponse.json({
            success: true,
            message: 'Neo4j schema initialized successfully',
            stats
          });
        } else {
          return NextResponse.json(
            { error: 'Failed to initialize Neo4j schema' },
            { status: 500 }
          );
        }

      case 'verify':
        console.log('Verifying Neo4j schema...');
        const isValid = await verifyNeo4jSchema();
        const stats = await getNeo4jStats();
        
        return NextResponse.json({
          success: true,
          schema_valid: isValid,
          message: isValid ? 'Schema is valid' : 'Schema validation failed',
          stats
        });

      case 'stats':
        console.log('Getting Neo4j statistics...');
        const currentStats = await getNeo4jStats();
        
        return NextResponse.json({
          success: true,
          stats: currentStats
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: initialize, verify, or stats' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in Neo4j schema initialization:', error);
    return NextResponse.json(
      { error: 'Schema operation failed', details: error.message },
      { status: 500 }
    );
  }
});

export async function GET(request: NextRequest) {
  try {
    // Public endpoint to check Neo4j status (no auth required)
    const stats = await getNeo4jStats();
    const isValid = await verifyNeo4jSchema();
    
    return NextResponse.json({
      neo4j_enabled: process.env.ENABLE_NEO4J === 'true',
      neo4j_insights_enabled: process.env.ENABLE_NEO4J_INSIGHTS === 'true',
      schema_valid: isValid,
      stats
    });
  } catch (error) {
    console.error('Error checking Neo4j status:', error);
    return NextResponse.json(
      { error: 'Failed to check Neo4j status', details: error.message },
      { status: 500 }
    );
  }
}
