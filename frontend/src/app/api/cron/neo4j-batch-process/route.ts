/**
 * Neo4j Batch Processing for Overnight Insights
 * 
 * This cron job processes tenant activities stored in Neo4j to generate
 * proactive insights and recommendations based on:
 * - Past activity patterns
 * - Current matters and deadlines
 * - Calendar events
 * - User behavior analysis
 * 
 * Runs overnight to prepare insights for the next day.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getDriver } from '@/lib/neo4j/driver';
import { recordInsightInNeo4j } from '@/lib/neo4j/insights';
import { z } from 'zod';

// Environment validation
const envSchema = z.object({
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  SUPABASE_SERVICE_KEY: z.string().min(1),
  CRON_SECRET: z.string().min(1).optional(),
  ENABLE_NEO4J: z.string().optional(),
});

const env = envSchema.safeParse({
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
  SUPABASE_SERVICE_KEY: process.env.SUPABASE_SERVICE_KEY,
  CRON_SECRET: process.env.CRON_SECRET,
  ENABLE_NEO4J: process.env.ENABLE_NEO4J,
});

if (!env.success) {
  console.error('Environment validation failed:', env.error);
}

const supabase = createClient(
  env.data?.NEXT_PUBLIC_SUPABASE_URL || '',
  env.data?.SUPABASE_SERVICE_KEY || ''
);

interface TenantInsight {
  tenant_id: string;
  user_id: string;
  insight_type: 'proactive_suggestion' | 'deadline_alert' | 'pattern_analysis' | 'workload_optimization';
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  suggested_actions: string[];
  related_activities: string[];
  metadata: Record<string, any>;
}

/**
 * Analyze tenant activity patterns in Neo4j
 */
async function analyzeTenantActivityPatterns(tenantId: string): Promise<any[]> {
  const driver = getDriver();
  if (!driver) {
    console.log('Neo4j not enabled, skipping pattern analysis');
    return [];
  }

  const session = driver.session();

  try {
    // Query to find activity patterns for the tenant
    const query = `
      MATCH (u:User {tenant_id: $tenantId})-[:PERFORMED]->(a:Activity)
      WHERE a.timestamp >= datetime() - duration('P7D') // Last 7 days
      
      // Group activities by type and user
      WITH u, a.activity_type as activityType, count(a) as activityCount,
           collect(a) as activities
      
      // Find users with high activity in specific areas
      WHERE activityCount >= 3
      
      // Get related cases and documents for context
      OPTIONAL MATCH (activities)-[:ON_CASE]->(c:Case)
      OPTIONAL MATCH (activities)-[:REFERENCES]->(d:Document)
      
      RETURN u.id as userId,
             activityType,
             activityCount,
             collect(DISTINCT c.id) as relatedCases,
             collect(DISTINCT d.id) as relatedDocuments,
             [act in activities | {
               id: act.id,
               timestamp: act.timestamp,
               summary: act.summary,
               importance: act.importance
             }] as activityDetails
      ORDER BY activityCount DESC
      LIMIT 20
    `;

    const result = await session.run(query, { tenantId });
    
    return result.records.map(record => ({
      userId: record.get('userId'),
      activityType: record.get('activityType'),
      activityCount: record.get('activityCount').toNumber(),
      relatedCases: record.get('relatedCases'),
      relatedDocuments: record.get('relatedDocuments'),
      activityDetails: record.get('activityDetails')
    }));
  } catch (error) {
    console.error('Error analyzing activity patterns:', error);
    return [];
  } finally {
    await session.close();
  }
}

/**
 * Find users with upcoming deadlines and related activities
 */
async function findDeadlineRelatedInsights(tenantId: string): Promise<TenantInsight[]> {
  const insights: TenantInsight[] = [];

  try {
    // Get upcoming deadlines from Supabase
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const { data: deadlines, error } = await supabase
      .schema('tenants')
      .from('deadlines')
      .select(`
        *,
        matters!inner(id, title, status),
        users!inner(id, email)
      `)
      .eq('matters.tenant_id', tenantId)
      .gte('due_date', new Date().toISOString())
      .lte('due_date', nextWeek.toISOString())
      .order('due_date', { ascending: true });

    if (error) throw error;

    // Analyze each deadline for related activities
    for (const deadline of deadlines || []) {
      const driver = getDriver();
      if (!driver) continue;

      const session = driver.session();

      try {
        // Find recent activities related to this case/matter
        const activityQuery = `
          MATCH (u:User {id: $userId})-[:PERFORMED]->(a:Activity)
          WHERE a.tenant_id = $tenantId
            AND (a.case_id = $caseId OR a.metadata CONTAINS $caseId)
            AND a.timestamp >= datetime() - duration('P14D')
          
          RETURN a.id as activityId,
                 a.activity_type as activityType,
                 a.summary as summary,
                 a.importance as importance,
                 a.timestamp as timestamp
          ORDER BY a.timestamp DESC
          LIMIT 10
        `;

        const activityResult = await session.run(activityQuery, {
          userId: deadline.users.id,
          tenantId,
          caseId: deadline.matters.id
        });

        const recentActivities = activityResult.records.map(record => ({
          id: record.get('activityId'),
          type: record.get('activityType'),
          summary: record.get('summary'),
          importance: record.get('importance'),
          timestamp: record.get('timestamp')
        }));

        // Generate insight based on deadline and recent activities
        if (recentActivities.length > 0) {
          const daysUntilDeadline = Math.ceil(
            (new Date(deadline.due_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          );

          let priority: 'high' | 'medium' | 'low' = 'medium';
          let suggestedActions: string[] = [];

          if (daysUntilDeadline <= 3) {
            priority = 'high';
            suggestedActions = [
              'Review deadline requirements immediately',
              'Prepare necessary documents',
              'Schedule time to complete tasks'
            ];
          } else if (daysUntilDeadline <= 7) {
            priority = 'medium';
            suggestedActions = [
              'Begin preparation for upcoming deadline',
              'Review related case materials',
              'Block calendar time for deadline work'
            ];
          }

          insights.push({
            tenant_id: tenantId,
            user_id: deadline.users.id,
            insight_type: 'deadline_alert',
            title: `Upcoming Deadline: ${deadline.title}`,
            description: `You have a deadline "${deadline.title}" due in ${daysUntilDeadline} days for matter "${deadline.matters.title}". Based on your recent activity, here are some suggestions.`,
            priority,
            suggested_actions: suggestedActions,
            related_activities: recentActivities.map(a => a.id),
            metadata: {
              deadline_id: deadline.id,
              matter_id: deadline.matters.id,
              matter_title: deadline.matters.title,
              due_date: deadline.due_date,
              days_until_deadline: daysUntilDeadline,
              recent_activity_count: recentActivities.length
            }
          });
        }
      } finally {
        await session.close();
      }
    }
  } catch (error) {
    console.error('Error finding deadline insights:', error);
  }

  return insights;
}

/**
 * Generate workload optimization insights
 */
async function generateWorkloadInsights(tenantId: string): Promise<TenantInsight[]> {
  const insights: TenantInsight[] = [];
  const patterns = await analyzeTenantActivityPatterns(tenantId);

  for (const pattern of patterns) {
    // Identify potential workload optimization opportunities
    if (pattern.activityCount > 10 && pattern.activityType === 'DOCUMENT_UPLOAD') {
      insights.push({
        tenant_id: tenantId,
        user_id: pattern.userId,
        insight_type: 'workload_optimization',
        title: 'High Document Activity Detected',
        description: `You've uploaded ${pattern.activityCount} documents in the past week. Consider organizing them into folders or using templates to streamline your workflow.`,
        priority: 'medium',
        suggested_actions: [
          'Create document templates for common types',
          'Organize documents into case-specific folders',
          'Consider batch processing similar documents'
        ],
        related_activities: pattern.activityDetails.map((a: any) => a.id),
        metadata: {
          activity_type: pattern.activityType,
          activity_count: pattern.activityCount,
          related_cases: pattern.relatedCases,
          time_period: '7_days'
        }
      });
    }

    if (pattern.activityCount > 15 && pattern.activityType === 'CASE_VIEWED') {
      insights.push({
        tenant_id: tenantId,
        user_id: pattern.userId,
        insight_type: 'pattern_analysis',
        title: 'Frequent Case Reviews',
        description: `You've reviewed cases ${pattern.activityCount} times this week. This might indicate you're looking for specific information. Consider creating case summaries or bookmarks.`,
        priority: 'low',
        suggested_actions: [
          'Create case summary notes',
          'Use bookmarks for frequently accessed information',
          'Set up case status alerts'
        ],
        related_activities: pattern.activityDetails.map((a: any) => a.id),
        metadata: {
          activity_type: pattern.activityType,
          activity_count: pattern.activityCount,
          related_cases: pattern.relatedCases
        }
      });
    }
  }

  return insights;
}

/**
 * Process insights for a single tenant
 */
async function processTenantInsights(tenantId: string): Promise<number> {
  console.log(`Processing insights for tenant: ${tenantId}`);

  try {
    // Generate different types of insights
    const [deadlineInsights, workloadInsights] = await Promise.all([
      findDeadlineRelatedInsights(tenantId),
      generateWorkloadInsights(tenantId)
    ]);

    const allInsights = [...deadlineInsights, ...workloadInsights];

    // Store insights in both Supabase and Neo4j
    let storedCount = 0;

    for (const insight of allInsights) {
      try {
        // Store in Supabase
        const { error: supabaseError } = await supabase
          .schema('tenants')
          .from('insights')
          .insert({
            id: `insight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            tenant_id: insight.tenant_id,
            user_id: insight.user_id,
            title: insight.title,
            description: insight.description,
            priority: insight.priority,
            source: 'neo4j_batch_processing',
            metadata: {
              ...insight.metadata,
              insight_type: insight.insight_type,
              suggested_actions: insight.suggested_actions,
              related_activities: insight.related_activities,
              generated_at: new Date().toISOString(),
              generation_method: 'overnight_batch'
            },
            created_at: new Date().toISOString()
          });

        if (supabaseError) {
          console.error('Error storing insight in Supabase:', supabaseError);
          continue;
        }

        // Store in Neo4j for relationship analysis
        await recordInsightInNeo4j(
          {
            message: insight.title,
            description: insight.description,
            priority: insight.priority,
            suggestions: insight.suggested_actions,
            aiGenerated: true,
            relatedActivities: insight.related_activities
          },
          insight.user_id,
          'batch_processing',
          null,
          insight.metadata
        );

        storedCount++;
      } catch (error) {
        console.error('Error storing individual insight:', error);
      }
    }

    console.log(`Generated ${storedCount} insights for tenant ${tenantId}`);
    return storedCount;
  } catch (error) {
    console.error(`Error processing insights for tenant ${tenantId}:`, error);
    return 0;
  }
}

/**
 * Main cron job handler
 */
export async function GET(request: NextRequest) {
  try {
    // Verify cron secret if provided
    const cronSecret = request.nextUrl.searchParams.get('secret');
    if (env.data?.CRON_SECRET && cronSecret !== env.data.CRON_SECRET) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if Neo4j is enabled
    if (env.data?.ENABLE_NEO4J !== 'true') {
      return NextResponse.json({ 
        message: 'Neo4j batch processing is disabled',
        enable_neo4j: env.data?.ENABLE_NEO4J 
      });
    }

    console.log('Starting Neo4j batch processing for overnight insights...');

    // Get all active tenants
    const { data: tenants, error: tenantError } = await supabase
      .schema('tenants')
      .from('firms')
      .select('tenant_id')
      .eq('status', 'active');

    if (tenantError) {
      throw new Error(`Error fetching tenants: ${tenantError.message}`);
    }

    if (!tenants || tenants.length === 0) {
      return NextResponse.json({ message: 'No active tenants found' });
    }

    // Process insights for each tenant
    let totalInsights = 0;
    const results = [];

    for (const tenant of tenants) {
      const insightCount = await processTenantInsights(tenant.tenant_id);
      totalInsights += insightCount;
      results.push({
        tenant_id: tenant.tenant_id,
        insights_generated: insightCount
      });
    }

    console.log(`Neo4j batch processing completed. Total insights generated: ${totalInsights}`);

    return NextResponse.json({
      success: true,
      message: `Neo4j batch processing completed`,
      total_insights: totalInsights,
      tenants_processed: tenants.length,
      results
    });

  } catch (error) {
    console.error('Error in Neo4j batch processing:', error);
    return NextResponse.json(
      { error: 'Neo4j batch processing failed', details: error.message },
      { status: 500 }
    );
  }
}
