/**
 * Neo4j Legal Precedents and Citation Network
 * 
 * This module handles legal document relationships, precedents, and citation networks
 * for the research agent.
 */

import { getDriver } from './driver';
import { v4 as uuidv4 } from 'uuid';

export interface LegalDocument {
  id: string;
  title: string;
  jurisdiction: string;
  document_type: 'statute' | 'case' | 'regulation' | 'rule' | 'opinion';
  publication_date?: string;
  content_summary?: string;
  citation?: string;
  court?: string;
  judge?: string;
  parties?: string[];
}

export interface CitationRelationship {
  citation_type: 'direct' | 'indirect' | 'distinguishing' | 'overruling' | 'following';
  strength: 'strong' | 'medium' | 'weak';
  context?: string;
  page_reference?: string;
}

export interface PrecedentRelationship {
  precedent_type: 'binding' | 'persuasive' | 'distinguishable' | 'overruled';
  binding_strength: 'mandatory' | 'highly_persuasive' | 'persuasive' | 'minimal';
  jurisdiction_match: 'same' | 'higher' | 'parallel' | 'lower' | 'different';
  reasoning?: string;
}

/**
 * Store a legal document in Neo4j
 */
export async function storeLegalDocument(document: LegalDocument): Promise<boolean> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return false;
  }

  const session = driver.session();

  try {
    const query = `
      MERGE (d:Document {id: $id})
      SET d.title = $title,
          d.jurisdiction = $jurisdiction,
          d.document_type = $document_type,
          d.publication_date = $publication_date,
          d.content_summary = $content_summary,
          d.citation = $citation,
          d.court = $court,
          d.judge = $judge,
          d.parties = $parties,
          d.updated_at = datetime()
      RETURN d.id as documentId
    `;

    const params = {
      id: document.id,
      title: document.title,
      jurisdiction: document.jurisdiction,
      document_type: document.document_type,
      publication_date: document.publication_date || null,
      content_summary: document.content_summary || null,
      citation: document.citation || null,
      court: document.court || null,
      judge: document.judge || null,
      parties: document.parties || []
    };

    await session.run(query, params);
    return true;
  } catch (error) {
    console.error('Error storing legal document:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Create a citation relationship between two legal documents
 */
export async function createCitationRelationship(
  citingDocId: string,
  citedDocId: string,
  relationship: CitationRelationship
): Promise<boolean> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return false;
  }

  const session = driver.session();

  try {
    const query = `
      MATCH (citing:Document {id: $citingDocId})
      MATCH (cited:Document {id: $citedDocId})
      MERGE (citing)-[r:CITES]->(cited)
      SET r.citation_type = $citation_type,
          r.strength = $strength,
          r.context = $context,
          r.page_reference = $page_reference,
          r.created_at = datetime()
      RETURN r
    `;

    const params = {
      citingDocId,
      citedDocId,
      citation_type: relationship.citation_type,
      strength: relationship.strength,
      context: relationship.context || null,
      page_reference: relationship.page_reference || null
    };

    await session.run(query, params);
    return true;
  } catch (error) {
    console.error('Error creating citation relationship:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Create a precedent relationship between two legal documents
 */
export async function createPrecedentRelationship(
  followingDocId: string,
  precedentDocId: string,
  relationship: PrecedentRelationship
): Promise<boolean> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return false;
  }

  const session = driver.session();

  try {
    const query = `
      MATCH (following:Document {id: $followingDocId})
      MATCH (precedent:Document {id: $precedentDocId})
      MERGE (following)-[r:PRECEDENT]->(precedent)
      SET r.precedent_type = $precedent_type,
          r.binding_strength = $binding_strength,
          r.jurisdiction_match = $jurisdiction_match,
          r.reasoning = $reasoning,
          r.created_at = datetime()
      RETURN r
    `;

    const params = {
      followingDocId,
      precedentDocId,
      precedent_type: relationship.precedent_type,
      binding_strength: relationship.binding_strength,
      jurisdiction_match: relationship.jurisdiction_match,
      reasoning: relationship.reasoning || null
    };

    await session.run(query, params);
    return true;
  } catch (error) {
    console.error('Error creating precedent relationship:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Find related legal documents through citation and precedent networks
 */
export async function findRelatedDocuments(
  documentId: string,
  maxDepth: number = 2,
  limit: number = 10
): Promise<any[]> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return [];
  }

  const session = driver.session();

  try {
    const query = `
      MATCH (start:Document {id: $documentId})
      CALL {
        WITH start
        MATCH path = (start)-[:CITES|PRECEDENT*1..${maxDepth}]-(related:Document)
        WHERE related.id <> start.id
        RETURN related, 
               length(path) as distance,
               'citation_network' as relationship_source,
               relationships(path) as path_relationships
        UNION
        WITH start
        MATCH path = (start)<-[:CITES|PRECEDENT*1..${maxDepth}]-(related:Document)
        WHERE related.id <> start.id
        RETURN related, 
               length(path) as distance,
               'reverse_citation' as relationship_source,
               relationships(path) as path_relationships
      }
      WITH related, distance, relationship_source, path_relationships
      ORDER BY distance ASC, related.publication_date DESC
      LIMIT $limit
      RETURN related.id as id,
             related.title as title,
             related.jurisdiction as jurisdiction,
             related.document_type as document_type,
             related.publication_date as publication_date,
             related.citation as citation,
             distance,
             relationship_source,
             [r in path_relationships | {type: type(r), properties: properties(r)}] as relationship_chain
    `;

    const params = {
      documentId,
      limit
    };

    const result = await session.run(query, params);
    
    return result.records.map(record => ({
      id: record.get('id'),
      title: record.get('title'),
      jurisdiction: record.get('jurisdiction'),
      document_type: record.get('document_type'),
      publication_date: record.get('publication_date'),
      citation: record.get('citation'),
      distance: record.get('distance').toNumber(),
      relationship_source: record.get('relationship_source'),
      relationship_chain: record.get('relationship_chain')
    }));
  } catch (error) {
    console.error('Error finding related documents:', error);
    return [];
  } finally {
    await session.close();
  }
}

/**
 * Find legal precedents for a specific legal concept or topic
 */
export async function findPrecedentsForConcept(
  conceptId: string,
  jurisdiction?: string,
  limit: number = 20
): Promise<any[]> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return [];
  }

  const session = driver.session();

  try {
    const query = `
      MATCH (concept:LegalConcept {id: $conceptId})
      MATCH (concept)<-[:ADDRESSES]-(doc:Document)
      ${jurisdiction ? 'WHERE doc.jurisdiction = $jurisdiction' : ''}
      OPTIONAL MATCH (doc)-[p:PRECEDENT]->(precedent:Document)
      WITH doc, collect({
        precedent: precedent,
        relationship: properties(p)
      }) as precedents
      ORDER BY doc.publication_date DESC
      LIMIT $limit
      RETURN doc.id as id,
             doc.title as title,
             doc.jurisdiction as jurisdiction,
             doc.document_type as document_type,
             doc.publication_date as publication_date,
             doc.citation as citation,
             precedents
    `;

    const params = {
      conceptId,
      jurisdiction: jurisdiction || null,
      limit
    };

    const result = await session.run(query, params);
    
    return result.records.map(record => ({
      id: record.get('id'),
      title: record.get('title'),
      jurisdiction: record.get('jurisdiction'),
      document_type: record.get('document_type'),
      publication_date: record.get('publication_date'),
      citation: record.get('citation'),
      precedents: record.get('precedents')
    }));
  } catch (error) {
    console.error('Error finding precedents for concept:', error);
    return [];
  } finally {
    await session.close();
  }
}

/**
 * Get citation analysis for a legal document
 */
export async function getCitationAnalysis(documentId: string): Promise<any> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available');
    return null;
  }

  const session = driver.session();

  try {
    const query = `
      MATCH (doc:Document {id: $documentId})
      
      // Count outgoing citations
      OPTIONAL MATCH (doc)-[out_cite:CITES]->(cited:Document)
      WITH doc, count(out_cite) as citations_made, collect(cited) as cited_docs
      
      // Count incoming citations
      OPTIONAL MATCH (citing:Document)-[in_cite:CITES]->(doc)
      WITH doc, citations_made, cited_docs, count(in_cite) as times_cited, collect(citing) as citing_docs
      
      // Count precedent relationships
      OPTIONAL MATCH (doc)-[prec:PRECEDENT]->(precedent:Document)
      WITH doc, citations_made, cited_docs, times_cited, citing_docs, count(prec) as precedents_followed
      
      // Count times used as precedent
      OPTIONAL MATCH (following:Document)-[follow:PRECEDENT]->(doc)
      WITH doc, citations_made, cited_docs, times_cited, citing_docs, precedents_followed, count(follow) as times_as_precedent
      
      RETURN {
        document_id: doc.id,
        title: doc.title,
        citations_made: citations_made,
        times_cited: times_cited,
        precedents_followed: precedents_followed,
        times_as_precedent: times_as_precedent,
        authority_score: times_cited + (times_as_precedent * 2),
        cited_documents: [d in cited_docs | {id: d.id, title: d.title}],
        citing_documents: [d in citing_docs | {id: d.id, title: d.title}]
      } as analysis
    `;

    const params = { documentId };
    const result = await session.run(query, params);
    
    if (result.records.length > 0) {
      return result.records[0].get('analysis');
    }
    
    return null;
  } catch (error) {
    console.error('Error getting citation analysis:', error);
    return null;
  } finally {
    await session.close();
  }
}
