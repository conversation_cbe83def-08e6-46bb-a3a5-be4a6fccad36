/**
 * Neo4j Schema Initialization
 * 
 * This module provides utilities to initialize the Neo4j database schema
 * for legal precedents and tenant activity tracking.
 */

import { getDriver } from './driver';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Initialize Neo4j schema by running the schema.cypher file
 * @returns Promise<boolean> indicating success
 */
export async function initializeNeo4jSchema(): Promise<boolean> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available for schema initialization');
    return false;
  }

  const session = driver.session();

  try {
    // Read the schema file
    const schemaPath = join(__dirname, 'schema.cypher');
    const schemaContent = readFileSync(schemaPath, 'utf-8');

    // Split the schema into individual statements
    const statements = schemaContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('//'));

    console.log(`Executing ${statements.length} schema statements...`);

    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await session.run(statement);
          console.log(`✓ Executed: ${statement.substring(0, 50)}...`);
        } catch (error) {
          console.warn(`⚠ Warning executing statement: ${error}`);
          // Continue with other statements even if one fails
        }
      }
    }

    console.log('✅ Neo4j schema initialization completed');
    return true;
  } catch (error) {
    console.error('❌ Error initializing Neo4j schema:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Verify Neo4j schema is properly set up
 * @returns Promise<boolean> indicating if schema is valid
 */
export async function verifyNeo4jSchema(): Promise<boolean> {
  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available for schema verification');
    return false;
  }

  const session = driver.session();

  try {
    // Check if required constraints exist
    const constraintQuery = `
      SHOW CONSTRAINTS
      YIELD name, type, entityType, labelsOrTypes, properties
      WHERE name IN [
        'user_id_unique', 
        'document_id_unique', 
        'case_id_unique', 
        'activity_id_unique', 
        'insight_id_unique'
      ]
      RETURN count(*) as constraintCount
    `;

    const constraintResult = await session.run(constraintQuery);
    const constraintCount = constraintResult.records[0]?.get('constraintCount')?.toNumber() || 0;

    // Check if required indexes exist
    const indexQuery = `
      SHOW INDEXES
      YIELD name, type, entityType, labelsOrTypes, properties
      WHERE name IN [
        'user_tenant_id',
        'document_jurisdiction',
        'activity_timestamp',
        'insight_priority'
      ]
      RETURN count(*) as indexCount
    `;

    const indexResult = await session.run(indexQuery);
    const indexCount = indexResult.records[0]?.get('indexCount')?.toNumber() || 0;

    // Check if sample data exists
    const dataQuery = `
      MATCH (concept:LegalConcept)
      RETURN count(concept) as conceptCount
    `;

    const dataResult = await session.run(dataQuery);
    const conceptCount = dataResult.records[0]?.get('conceptCount')?.toNumber() || 0;

    console.log(`Schema verification results:`);
    console.log(`- Constraints: ${constraintCount}/5`);
    console.log(`- Indexes: ${indexCount}/4`);
    console.log(`- Legal concepts: ${conceptCount}`);

    const isValid = constraintCount >= 5 && indexCount >= 4;
    
    if (isValid) {
      console.log('✅ Neo4j schema verification passed');
    } else {
      console.log('❌ Neo4j schema verification failed');
    }

    return isValid;
  } catch (error) {
    console.error('Error verifying Neo4j schema:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Reset Neo4j database (WARNING: This will delete all data)
 * Only use in development/testing environments
 * @returns Promise<boolean> indicating success
 */
export async function resetNeo4jDatabase(): Promise<boolean> {
  if (process.env.NODE_ENV === 'production') {
    console.error('❌ Database reset is not allowed in production environment');
    return false;
  }

  const driver = getDriver();
  if (!driver) {
    console.error('Neo4j driver not available for database reset');
    return false;
  }

  const session = driver.session();

  try {
    console.log('⚠️  Resetting Neo4j database...');

    // Delete all nodes and relationships
    await session.run('MATCH (n) DETACH DELETE n');

    // Drop all constraints
    const constraints = await session.run('SHOW CONSTRAINTS YIELD name');
    for (const record of constraints.records) {
      const constraintName = record.get('name');
      try {
        await session.run(`DROP CONSTRAINT ${constraintName}`);
      } catch (error) {
        console.warn(`Warning dropping constraint ${constraintName}:`, error);
      }
    }

    // Drop all indexes
    const indexes = await session.run('SHOW INDEXES YIELD name WHERE type <> "LOOKUP"');
    for (const record of indexes.records) {
      const indexName = record.get('name');
      try {
        await session.run(`DROP INDEX ${indexName}`);
      } catch (error) {
        console.warn(`Warning dropping index ${indexName}:`, error);
      }
    }

    console.log('✅ Neo4j database reset completed');
    return true;
  } catch (error) {
    console.error('❌ Error resetting Neo4j database:', error);
    return false;
  } finally {
    await session.close();
  }
}

/**
 * Get Neo4j database statistics
 * @returns Promise<object> with database statistics
 */
export async function getNeo4jStats(): Promise<any> {
  const driver = getDriver();
  if (!driver) {
    return { error: 'Neo4j driver not available' };
  }

  const session = driver.session();

  try {
    // Get node counts by label
    const nodeQuery = `
      CALL db.labels() YIELD label
      CALL apoc.cypher.run('MATCH (n:' + label + ') RETURN count(n) as count', {})
      YIELD value
      RETURN label, value.count as count
    `;

    // Get relationship counts by type
    const relQuery = `
      CALL db.relationshipTypes() YIELD relationshipType
      CALL apoc.cypher.run('MATCH ()-[r:' + relationshipType + ']->() RETURN count(r) as count', {})
      YIELD value
      RETURN relationshipType, value.count as count
    `;

    const [nodeResult, relResult] = await Promise.all([
      session.run(nodeQuery).catch(() => ({ records: [] })),
      session.run(relQuery).catch(() => ({ records: [] }))
    ]);

    const nodeStats = nodeResult.records.reduce((acc: any, record) => {
      acc[record.get('label')] = record.get('count').toNumber();
      return acc;
    }, {});

    const relationshipStats = relResult.records.reduce((acc: any, record) => {
      acc[record.get('relationshipType')] = record.get('count').toNumber();
      return acc;
    }, {});

    return {
      nodes: nodeStats,
      relationships: relationshipStats,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting Neo4j stats:', error);
    return { error: error.message };
  } finally {
    await session.close();
  }
}
