// Neo4j Schema Initialization for PI Lawyer AI
// This file contains Cypher queries to set up the Neo4j database schema
// for legal precedents and tenant activity tracking

// ============================================================================
// CONSTRAINTS AND INDEXES
// ============================================================================

// User constraints and indexes
CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE;
CREATE INDEX user_tenant_id IF NOT EXISTS FOR (u:User) ON (u.tenant_id);

// Legal Document constraints and indexes
CREATE CONSTRAINT document_id_unique IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE;
CREATE INDEX document_jurisdiction IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction);
CREATE INDEX document_type IF NOT EXISTS FOR (d:Document) ON (d.document_type);
CREATE INDEX document_publication_date IF NOT EXISTS FOR (d:Document) ON (d.publication_date);

// Case constraints and indexes
CREATE CONSTRAINT case_id_unique IF NOT EXISTS FOR (c:Case) REQUIRE c.id IS UNIQUE;
CREATE INDEX case_tenant_id IF NOT EXISTS FOR (c:Case) ON (c.tenant_id);
CREATE INDEX case_status IF NOT EXISTS FOR (c:Case) ON (c.status);

// Activity constraints and indexes
CREATE CONSTRAINT activity_id_unique IF NOT EXISTS FOR (a:Activity) REQUIRE a.id IS UNIQUE;
CREATE INDEX activity_tenant_id IF NOT EXISTS FOR (a:Activity) ON (a.tenant_id);
CREATE INDEX activity_timestamp IF NOT EXISTS FOR (a:Activity) ON (a.timestamp);
CREATE INDEX activity_type IF NOT EXISTS FOR (a:Activity) ON (a.activity_type);

// Insight constraints and indexes
CREATE CONSTRAINT insight_id_unique IF NOT EXISTS FOR (i:Insight) REQUIRE i.id IS UNIQUE;
CREATE INDEX insight_timestamp IF NOT EXISTS FOR (i:Insight) ON (i.timestamp);
CREATE INDEX insight_priority IF NOT EXISTS FOR (i:Insight) ON (i.priority);
CREATE INDEX insight_source IF NOT EXISTS FOR (i:Insight) ON (i.source);

// Legal precedent and citation indexes
CREATE INDEX citation_relationship IF NOT EXISTS FOR ()-[r:CITES]-() ON (r.citation_type);
CREATE INDEX precedent_relationship IF NOT EXISTS FOR ()-[r:PRECEDENT]-() ON (r.precedent_type);

// ============================================================================
// LEGAL PRECEDENTS AND RELATIONSHIPS SCHEMA
// ============================================================================

// Legal Document nodes represent statutes, cases, regulations, etc.
// Properties: id, title, jurisdiction, document_type, publication_date, content_summary

// Citation relationships between legal documents
// (:Document)-[:CITES {citation_type, strength, context}]->(:Document)

// Precedent relationships for case law
// (:Document)-[:PRECEDENT {precedent_type, binding_strength, jurisdiction_match}]->(:Document)

// Legal concepts and topics
// (:Document)-[:ADDRESSES]->(:LegalConcept)
// (:LegalConcept)-[:RELATED_TO]->(:LegalConcept)

// ============================================================================
// TENANT ACTIVITY TRACKING SCHEMA
// ============================================================================

// User activity tracking
// (:User)-[:PERFORMED]->(:Activity)
// (:Activity)-[:ON_CASE]->(:Case)
// (:Activity)-[:REFERENCES]->(:Document)
// (:Activity)-[:TO]->(:Contact)

// Activity types and patterns
// (:Activity)-[:OF_TYPE]->(:ActivityType)
// (:ActivityType)-[:FOLLOWS]->(:ActivityType) // Sequential patterns

// Insights and recommendations
// (:User)-[:HAS_INSIGHT]->(:Insight)
// (:Insight)-[:DERIVED_FROM]->(:Activity)
// (:Insight)-[:HAS_CONTEXT]->(:Context)

// User feedback on insights
// (:User)-[:PROVIDED]->(:InsightFeedback)
// (:InsightFeedback)-[:FOR_INSIGHT]->(:Insight)

// ============================================================================
// SAMPLE DATA SETUP (Optional - for development/testing)
// ============================================================================

// Create sample legal concepts
MERGE (concept1:LegalConcept {id: 'personal-injury', name: 'Personal Injury Law'})
MERGE (concept2:LegalConcept {id: 'negligence', name: 'Negligence'})
MERGE (concept3:LegalConcept {id: 'damages', name: 'Damages'})
MERGE (concept4:LegalConcept {id: 'liability', name: 'Liability'})

// Create relationships between legal concepts
MERGE (concept2)-[:RELATED_TO {relationship_type: 'component_of'}]->(concept1)
MERGE (concept3)-[:RELATED_TO {relationship_type: 'remedy_for'}]->(concept2)
MERGE (concept4)-[:RELATED_TO {relationship_type: 'prerequisite_for'}]->(concept3);

// Create sample activity types
MERGE (at1:ActivityType {name: 'DOCUMENT_UPLOAD', category: 'document_management'})
MERGE (at2:ActivityType {name: 'CASE_VIEWED', category: 'case_management'})
MERGE (at3:ActivityType {name: 'EMAIL_SENT', category: 'communication'})
MERGE (at4:ActivityType {name: 'RESEARCH_PERFORMED', category: 'legal_research'})
MERGE (at5:ActivityType {name: 'DEADLINE_CREATED', category: 'task_management'});

// Create activity flow patterns
MERGE (at1)-[:FOLLOWS {probability: 0.7, avg_time_between: 'PT30M'}]->(at4)
MERGE (at4)-[:FOLLOWS {probability: 0.5, avg_time_between: 'PT1H'}]->(at3)
MERGE (at2)-[:FOLLOWS {probability: 0.8, avg_time_between: 'PT15M'}]->(at5);
