#!/usr/bin/env node

/**
 * Neo4j Setup Script
 * 
 * This script initializes the Neo4j database schema for the PI Lawyer AI system.
 * It should be run once during initial setup or when schema updates are needed.
 * 
 * Usage:
 *   node scripts/setup-neo4j.js
 *   
 * Environment variables required:
 *   - NEO4J_URI
 *   - NEO4J_USER  
 *   - NEO4J_PASSWORD
 *   - ENABLE_NEO4J=true
 */

const neo4j = require('neo4j-driver');
require('dotenv').config();

// Environment variables
const NEO4J_URI = process.env.NEO4J_URI || 'bolt://localhost:7687';
const NEO4J_USER = process.env.NEO4J_USER || 'neo4j';
const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD || 'neo4j';
const ENABLE_NEO4J = process.env.ENABLE_NEO4J === 'true';

console.log('🚀 Neo4j Setup Script for PI Lawyer AI');
console.log('=====================================');

if (!ENABLE_NEO4J) {
  console.error('❌ Neo4j is not enabled. Set ENABLE_NEO4J=true in your environment.');
  process.exit(1);
}

if (!NEO4J_URI || !NEO4J_USER || !NEO4J_PASSWORD) {
  console.error('❌ Missing required Neo4j environment variables:');
  console.error('   - NEO4J_URI');
  console.error('   - NEO4J_USER');
  console.error('   - NEO4J_PASSWORD');
  process.exit(1);
}

// Schema statements
const schemaStatements = [
  // Constraints
  'CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE',
  'CREATE CONSTRAINT document_id_unique IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE',
  'CREATE CONSTRAINT case_id_unique IF NOT EXISTS FOR (c:Case) REQUIRE c.id IS UNIQUE',
  'CREATE CONSTRAINT activity_id_unique IF NOT EXISTS FOR (a:Activity) REQUIRE a.id IS UNIQUE',
  'CREATE CONSTRAINT insight_id_unique IF NOT EXISTS FOR (i:Insight) REQUIRE i.id IS UNIQUE',
  
  // Indexes
  'CREATE INDEX user_tenant_id IF NOT EXISTS FOR (u:User) ON (u.tenant_id)',
  'CREATE INDEX document_jurisdiction IF NOT EXISTS FOR (d:Document) ON (d.jurisdiction)',
  'CREATE INDEX document_type IF NOT EXISTS FOR (d:Document) ON (d.document_type)',
  'CREATE INDEX activity_tenant_id IF NOT EXISTS FOR (a:Activity) ON (a.tenant_id)',
  'CREATE INDEX activity_timestamp IF NOT EXISTS FOR (a:Activity) ON (a.timestamp)',
  'CREATE INDEX activity_type IF NOT EXISTS FOR (a:Activity) ON (a.activity_type)',
  'CREATE INDEX insight_timestamp IF NOT EXISTS FOR (i:Insight) ON (i.timestamp)',
  'CREATE INDEX insight_priority IF NOT EXISTS FOR (i:Insight) ON (i.priority)',
  
  // Sample legal concepts
  `MERGE (concept1:LegalConcept {id: 'personal-injury', name: 'Personal Injury Law'})`,
  `MERGE (concept2:LegalConcept {id: 'negligence', name: 'Negligence'})`,
  `MERGE (concept3:LegalConcept {id: 'damages', name: 'Damages'})`,
  `MERGE (concept4:LegalConcept {id: 'liability', name: 'Liability'})`,
  
  // Legal concept relationships
  `MATCH (concept2:LegalConcept {id: 'negligence'}), (concept1:LegalConcept {id: 'personal-injury'})
   MERGE (concept2)-[:RELATED_TO {relationship_type: 'component_of'}]->(concept1)`,
  
  `MATCH (concept3:LegalConcept {id: 'damages'}), (concept2:LegalConcept {id: 'negligence'})
   MERGE (concept3)-[:RELATED_TO {relationship_type: 'remedy_for'}]->(concept2)`,
  
  // Sample activity types
  `MERGE (at1:ActivityType {name: 'DOCUMENT_UPLOAD', category: 'document_management'})`,
  `MERGE (at2:ActivityType {name: 'CASE_VIEWED', category: 'case_management'})`,
  `MERGE (at3:ActivityType {name: 'EMAIL_SENT', category: 'communication'})`,
  `MERGE (at4:ActivityType {name: 'RESEARCH_PERFORMED', category: 'legal_research'})`,
  `MERGE (at5:ActivityType {name: 'DEADLINE_CREATED', category: 'task_management'})`
];

async function setupNeo4j() {
  let driver;
  
  try {
    console.log(`📡 Connecting to Neo4j at ${NEO4J_URI}...`);
    
    // Create driver
    driver = neo4j.driver(
      NEO4J_URI,
      neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD),
      {
        maxConnectionPoolSize: 10,
        connectionAcquisitionTimeout: 5000,
        disableLosslessIntegers: true
      }
    );

    // Test connection
    const session = driver.session();
    await session.run('RETURN 1 as test');
    await session.close();
    
    console.log('✅ Connected to Neo4j successfully');
    
    // Execute schema statements
    console.log(`📋 Executing ${schemaStatements.length} schema statements...`);
    
    for (let i = 0; i < schemaStatements.length; i++) {
      const statement = schemaStatements[i];
      const session = driver.session();
      
      try {
        await session.run(statement);
        console.log(`✓ [${i + 1}/${schemaStatements.length}] ${statement.substring(0, 50)}...`);
      } catch (error) {
        console.warn(`⚠️ [${i + 1}/${schemaStatements.length}] Warning: ${error.message}`);
      } finally {
        await session.close();
      }
    }
    
    // Verify setup
    console.log('\n🔍 Verifying setup...');
    const verifySession = driver.session();
    
    try {
      // Check constraints
      const constraintResult = await verifySession.run(`
        SHOW CONSTRAINTS
        YIELD name, type, entityType, labelsOrTypes, properties
        WHERE name IN [
          'user_id_unique', 
          'document_id_unique', 
          'case_id_unique', 
          'activity_id_unique', 
          'insight_id_unique'
        ]
        RETURN count(*) as constraintCount
      `);
      
      const constraintCount = constraintResult.records[0]?.get('constraintCount')?.toNumber() || 0;
      
      // Check indexes
      const indexResult = await verifySession.run(`
        SHOW INDEXES
        YIELD name, type, entityType, labelsOrTypes, properties
        WHERE name IN [
          'user_tenant_id',
          'activity_timestamp',
          'insight_priority'
        ]
        RETURN count(*) as indexCount
      `);
      
      const indexCount = indexResult.records[0]?.get('indexCount')?.toNumber() || 0;
      
      // Check sample data
      const conceptResult = await verifySession.run(`
        MATCH (concept:LegalConcept)
        RETURN count(concept) as conceptCount
      `);
      
      const conceptCount = conceptResult.records[0]?.get('conceptCount')?.toNumber() || 0;
      
      console.log('\n📊 Setup Results:');
      console.log(`   Constraints: ${constraintCount}/5`);
      console.log(`   Indexes: ${indexCount}/3+`);
      console.log(`   Legal Concepts: ${conceptCount}`);
      
      if (constraintCount >= 5 && indexCount >= 3 && conceptCount >= 4) {
        console.log('\n🎉 Neo4j setup completed successfully!');
        console.log('\n📝 Next steps:');
        console.log('   1. Deploy your application with ENABLE_NEO4J=true');
        console.log('   2. Test activity logging by using the application');
        console.log('   3. Check overnight insights generation');
        console.log('   4. Monitor Neo4j performance and storage');
      } else {
        console.log('\n⚠️ Setup completed with warnings. Some components may not have been created properly.');
      }
      
    } finally {
      await verifySession.close();
    }
    
  } catch (error) {
    console.error('\n❌ Error during Neo4j setup:', error.message);
    
    if (error.code === 'ServiceUnavailable') {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   - Check if Neo4j URI is correct');
      console.error('   - Verify Neo4j instance is running');
      console.error('   - Check network connectivity');
    } else if (error.code === 'Neo.ClientError.Security.Unauthorized') {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   - Check Neo4j username and password');
      console.error('   - Verify credentials in your .env file');
    }
    
    process.exit(1);
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

// Run the setup
setupNeo4j().catch(console.error);
